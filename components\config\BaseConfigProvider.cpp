#include "BaseConfigProvider.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include <QMutexLocker>

namespace Config {

BaseConfigProvider::BaseConfigProvider(QObject *parent) : IConfigProvider(parent), m_fileWatcher(new QFileSystemWatcher(this)), m_fileWatcherEnabled(true) {
    // 连接文件监控信号
    connect(m_fileWatcher, &QFileSystemWatcher::fileChanged, this, &BaseConfigProvider::onConfigFileChanged);

    // 加载默认参数
    loadDefaultParameters();

    // 初始化文件监控
    initializeFileWatcher();
}

BaseConfigProvider::~BaseConfigProvider() {
    cleanupFileWatcher();
}

ConfigResult BaseConfigProvider::loadConfig() {
    QMutexLocker locker(&m_mutex);

    QString filePath = getConfigFilePath();
    logInfo(QString("Loading configuration from: %1").arg(filePath));

    // 如果文件不存在，创建默认配置文件
    if (!QFile::exists(filePath)) {
        logInfo("Configuration file not found, creating default configuration");
        ConfigResult result = saveToFile();
        if (!result.success) {
            return result;
        }
    }

    // 从文件加载配置
    ConfigResult result = loadFromFile();
    if (!result.success) {
        logError(QString("Failed to load configuration: %1").arg(result.message));
        return result;
    }

    // 验证配置
    ConfigResult validationResult = validateConfig();
    if (!validationResult.success) {
        logError(QString("Configuration validation failed: %1").arg(validationResult.message));
        return validationResult;
    }

    // 更新文件监控
    initializeFileWatcher();

    logInfo("Configuration loaded successfully");
    Q_EMIT configLoaded(getModuleName(), true);

    return ConfigResult(true);
}

ConfigResult BaseConfigProvider::saveConfig() {
    QMutexLocker locker(&m_mutex);

    QString filePath = getConfigFilePath();
    logInfo(QString("Saving configuration to: %1").arg(filePath));

    // 确保目录存在
    if (!ensureConfigDirectory()) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to create configuration directory");
    }

    // 保存到文件
    ConfigResult result = saveToFile();
    if (!result.success) {
        logError(QString("Failed to save configuration: %1").arg(result.message));
        Q_EMIT configSaved(getModuleName(), false);
        return result;
    }

    logInfo("Configuration saved successfully");
    Q_EMIT configSaved(getModuleName(), true);

    return ConfigResult(true);
}

ConfigResult BaseConfigProvider::validateConfig() const {
    QMutexLocker locker(&m_mutex);

    // 验证所有参数
    for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
        ConfigResult result = validateParameter(it.key(), it.value());
        if (!result.success) {
            return result;
        }
    }

    return ConfigResult(true);
}

QVariant BaseConfigProvider::getParameter(const QString &key, const QVariant &defaultValue) const {
    QMutexLocker locker(&m_mutex);

    if (m_parameters.contains(key)) {
        return m_parameters.value(key);
    }

    // 如果参数不存在，返回默认值
    if (m_defaultParameters.contains(key)) {
        return m_defaultParameters.value(key);
    }

    return defaultValue;
}

bool BaseConfigProvider::setParameter(const QString &key, const QVariant &value) {
    QMutexLocker locker(&m_mutex);

    // 获取旧值
    QVariant oldValue = m_parameters.value(key);

    // 验证新值
    ConfigResult validationResult = validateParameter(key, value);
    if (!validationResult.success) {
        logError(QString("Parameter validation failed for %1: %2").arg(key, validationResult.message));
        return false;
    }

    // 调用变更前处理
    if (!onParameterChanging(key, oldValue, value)) {
        logError(QString("Parameter change rejected for %1").arg(key));
        return false;
    }

    // 设置新值
    m_parameters[key] = value;

    // 调用变更后处理
    onParameterChanged(key, oldValue, value);

    // 发出变更信号
    locker.unlock();
    emitConfigChanged(key, oldValue, value);

    return true;
}

QVariantMap BaseConfigProvider::getAllParameters() const {
    QMutexLocker locker(&m_mutex);
    return m_parameters;
}

ConfigResult BaseConfigProvider::resetToDefault() {
    QMutexLocker locker(&m_mutex);

    logInfo("Resetting configuration to default values");

    // 备份当前参数
    QVariantMap oldParameters = m_parameters;

    // 重置为默认值
    m_parameters = m_defaultParameters;

    // 保存到文件
    ConfigResult result = saveToFile();
    if (!result.success) {
        // 恢复原参数
        m_parameters = oldParameters;
        logError(QString("Failed to reset configuration: %1").arg(result.message));
        return result;
    }

    // 发出所有参数变更信号
    locker.unlock();
    for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
        QVariant oldValue = oldParameters.value(it.key());
        if (oldValue != it.value()) {
            emitConfigChanged(it.key(), oldValue, it.value());
        }
    }

    logInfo("Configuration reset to default values successfully");
    return ConfigResult(true);
}

bool BaseConfigProvider::hasParameter(const QString &key) const {
    QMutexLocker locker(&m_mutex);
    return m_parameters.contains(key) || m_defaultParameters.contains(key);
}

QString BaseConfigProvider::getParameterType(const QString &key) const {
    QMutexLocker locker(&m_mutex);

    if (m_parameterInfo.contains(key)) {
        return m_parameterInfo[key].type;
    }

    // 根据值推断类型
    if (m_parameters.contains(key)) {
        return m_parameters[key].typeName();
    }

    if (m_defaultParameters.contains(key)) {
        return m_defaultParameters[key].typeName();
    }

    return "Unknown";
}

QString BaseConfigProvider::getParameterDescription(const QString &key) const {
    QMutexLocker locker(&m_mutex);

    if (m_parameterInfo.contains(key)) {
        return m_parameterInfo[key].description;
    }

    return QString("Parameter: %1").arg(key);
}

ConfigResult BaseConfigProvider::validateParameter(const QString &key, const QVariant &value) const {
    // 基础验证：检查值是否为空（如果不允许空值）
    if (value.isNull() || !value.isValid()) {
        return ConfigResult(false, ErrorType::ValidationError, QString("Parameter %1 cannot be null or invalid").arg(key));
    }

    // 子类可以重写此方法进行更详细的验证
    return ConfigResult(true);
}

QString BaseConfigProvider::getParameterRange(const QString &key) const {
    QMutexLocker locker(&m_mutex);

    if (m_parameterInfo.contains(key)) {
        return m_parameterInfo[key].range;
    }

    return "Not specified";
}

bool BaseConfigProvider::onParameterChanging(const QString &key, const QVariant &oldValue, const QVariant &newValue) {
    Q_UNUSED(key)
    Q_UNUSED(oldValue)
    Q_UNUSED(newValue)

    // 默认允许所有变更，子类可以重写此方法
    return true;
}

void BaseConfigProvider::onParameterChanged(const QString &key, const QVariant &oldValue, const QVariant &newValue) {
    Q_UNUSED(key)
    Q_UNUSED(oldValue)
    Q_UNUSED(newValue)

    // 子类可以重写此方法进行变更后处理
}

void BaseConfigProvider::setParameterInfo(const QString &key, const QString &description, const QString &type, const QString &range) {
    QMutexLocker locker(&m_mutex);

    ParameterInfo info;
    info.description = description;
    info.type        = type;
    info.range       = range;

    m_parameterInfo[key] = info;
}

void BaseConfigProvider::setParameterDefault(const QString &key, const QVariant &defaultValue) {
    QMutexLocker locker(&m_mutex);
    m_defaultParameters[key] = defaultValue;

    // 如果当前参数不存在，设置为默认值
    if (!m_parameters.contains(key)) {
        m_parameters[key] = defaultValue;
    }
}

void BaseConfigProvider::setParameters(const QVariantMap &parameters) {
    QMutexLocker locker(&m_mutex);

    for (auto it = parameters.begin(); it != parameters.end(); ++it) {
        m_parameters[it.key()] = it.value();
    }
}

void BaseConfigProvider::onConfigFileChanged(const QString &path) {
    if (!m_fileWatcherEnabled) {
        return;
    }

    QString configPath = getConfigFilePath();
    if (path == configPath) {
        logInfo("Configuration file changed, reloading...");
        loadConfig();
    }
}

void BaseConfigProvider::initializeFileWatcher() {
    if (!m_fileWatcher) {
        return;
    }

    QString filePath = getConfigFilePath();
    if (filePath.isEmpty()) {
        return;
    }

    // 移除旧的监控路径
    if (!m_lastConfigFilePath.isEmpty() && m_fileWatcher->files().contains(m_lastConfigFilePath)) {
        m_fileWatcher->removePath(m_lastConfigFilePath);
    }

    // 添加新的监控路径
    if (QFile::exists(filePath) && !m_fileWatcher->files().contains(filePath)) {
        m_fileWatcher->addPath(filePath);
        m_lastConfigFilePath = filePath;
    }
}

void BaseConfigProvider::cleanupFileWatcher() {
    if (m_fileWatcher) {
        m_fileWatcher->removePaths(m_fileWatcher->files());
    }
}

bool BaseConfigProvider::ensureConfigDirectory() {
    QString   filePath = getConfigFilePath();
    QFileInfo fileInfo(filePath);
    QString   dirPath = fileInfo.absolutePath();

    QDir dir;
    if (!dir.exists(dirPath)) {
        if (!dir.mkpath(dirPath)) {
            logError(QString("Failed to create configuration directory: %1").arg(dirPath));
            return false;
        }
        logInfo(QString("Created configuration directory: %1").arg(dirPath));
    }

    return true;
}

ConfigResult BaseConfigProvider::loadFromFile() {
    QString filePath = getConfigFilePath();

    QSettings settings(filePath, QSettings::IniFormat);
    if (settings.status() != QSettings::NoError) {
        return ConfigResult(false, ErrorType::FileReadError, QString("Failed to read configuration file: %1").arg(filePath));
    }

    // 读取所有配置项
    QStringList keys = settings.allKeys();
    for (const QString &key : keys) {
        QVariant value    = settings.value(key);
        m_parameters[key] = value;
    }

    return ConfigResult(true);
}

ConfigResult BaseConfigProvider::saveToFile() {
    QString filePath = getConfigFilePath();

    QSettings settings(filePath, QSettings::IniFormat);

    // 清除现有配置
    settings.clear();

    // 写入所有参数
    for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
        settings.setValue(it.key(), it.value());
    }

    settings.sync();

    if (settings.status() != QSettings::NoError) {
        return ConfigResult(false, ErrorType::FileWriteError, QString("Failed to write configuration file: %1").arg(filePath));
    }

    return ConfigResult(true);
}

void BaseConfigProvider::emitConfigChanged(const QString &key, const QVariant &oldValue, const QVariant &newValue) {
    Q_EMIT configChanged(getModuleName(), key, oldValue, newValue);
}

}  // namespace Config

// 实现IConfigProvider的辅助方法
namespace Config {

void IConfigProvider::logInfo(const QString &message) const {
    qDebug() << QString("[%1ConfigProvider]").arg(getModuleName()) << message;
}

void IConfigProvider::logError(const QString &message) const {
    qCritical() << QString("[%1ConfigProvider]").arg(getModuleName()) << message;
}

bool IConfigProvider::ensureDirectoryExists(const QString &dirPath) const {
    QDir dir;
    if (!dir.exists(dirPath)) {
        if (!dir.mkpath(dirPath)) {
            logError(QString("Failed to create directory: %1").arg(dirPath));
            return false;
        }
        logInfo(QString("Created directory: %1").arg(dirPath));
    }
    return true;
}

}  // namespace Config
