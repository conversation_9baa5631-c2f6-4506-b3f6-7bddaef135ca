#pragma once

#include <QString>
#include <QVariant>
#include <QObject>

namespace Config {

// 前向声明
struct ConfigResult;

/**
 * @brief 配置提供者接口
 * 
 * 每个模块都应该实现这个接口来提供自己的配置服务
 * 这样实现了配置的完全模块化和解耦
 * 
 * 设计原则：
 * - 开闭原则：新增模块不需要修改现有代码
 * - 单一职责：每个模块只管理自己的配置
 * - 依赖倒置：依赖抽象接口，不依赖具体实现
 */
class IConfigProvider : public QObject {
    Q_OBJECT

public:
    virtual ~IConfigProvider() = default;

    /**
     * @brief 获取模块名称
     * @return 模块名称，用于标识和注册
     */
    virtual QString getModuleName() const = 0;

    /**
     * @brief 获取模块版本
     * @return 模块配置版本，用于兼容性检查
     */
    virtual QString getModuleVersion() const { return "1.0.0"; }

    /**
     * @brief 获取配置文件路径
     * @return 配置文件的完整路径
     */
    virtual QString getConfigFilePath() const = 0;

    /**
     * @brief 加载配置
     * @return 加载结果
     */
    virtual ConfigResult loadConfig() = 0;

    /**
     * @brief 保存配置
     * @return 保存结果
     */
    virtual ConfigResult saveConfig() = 0;

    /**
     * @brief 验证配置有效性
     * @return 验证结果
     */
    virtual ConfigResult validateConfig() const = 0;

    /**
     * @brief 获取配置参数
     * @param key 参数名
     * @param defaultValue 默认值
     * @return 参数值
     */
    virtual QVariant getParameter(const QString &key, const QVariant &defaultValue = QVariant()) const = 0;

    /**
     * @brief 设置配置参数
     * @param key 参数名
     * @param value 参数值
     * @return 设置是否成功
     */
    virtual bool setParameter(const QString &key, const QVariant &value) = 0;

    /**
     * @brief 获取所有配置参数
     * @return 参数映射表
     */
    virtual QVariantMap getAllParameters() const = 0;

    /**
     * @brief 重置为默认配置
     * @return 重置结果
     */
    virtual ConfigResult resetToDefault() = 0;

    /**
     * @brief 检查参数是否存在
     * @param key 参数名
     * @return 是否存在
     */
    virtual bool hasParameter(const QString &key) const = 0;

    /**
     * @brief 获取参数类型信息
     * @param key 参数名
     * @return 参数类型描述
     */
    virtual QString getParameterType(const QString &key) const = 0;

    /**
     * @brief 获取参数描述信息
     * @param key 参数名
     * @return 参数描述
     */
    virtual QString getParameterDescription(const QString &key) const = 0;

Q_SIGNALS:
    /**
     * @brief 配置变更信号
     * @param moduleName 模块名称
     * @param key 变更的参数名
     * @param oldValue 旧值
     * @param newValue 新值
     */
    void configChanged(const QString &moduleName, const QString &key, const QVariant &oldValue, const QVariant &newValue);

    /**
     * @brief 配置加载完成信号
     * @param moduleName 模块名称
     * @param success 是否成功
     */
    void configLoaded(const QString &moduleName, bool success);

    /**
     * @brief 配置保存完成信号
     * @param moduleName 模块名称
     * @param success 是否成功
     */
    void configSaved(const QString &moduleName, bool success);

public Q_SLOTS:
    /**
     * @brief 配置文件变更通知
     * 当配置文件被外部修改时调用
     */
    virtual void onConfigFileChanged() {
        loadConfig();
    }

    /**
     * @brief 重新加载配置
     */
    virtual void reloadConfig() {
        loadConfig();
    }

protected:
    /**
     * @brief 记录日志信息
     * @param message 日志消息
     */
    void logInfo(const QString &message) const;

    /**
     * @brief 记录错误信息
     * @param message 错误消息
     */
    void logError(const QString &message) const;

    /**
     * @brief 确保配置目录存在
     * @param dirPath 目录路径
     * @return 是否成功
     */
    bool ensureDirectoryExists(const QString &dirPath) const;
};

} // namespace Config
