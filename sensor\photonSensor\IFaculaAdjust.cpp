#include "IFaculaAdjust.h"
#include "../../qtDebug/qLog.h"
#include "AdjustProcessConfigProvider.h"
#include "AlgorithmConfigProvider.h"
#include <QApplication>
#include <QFile>


QMap<int, QString> IFaculaAdjust::mm_adjust_step_discribe = {
    {0x0000, " OK"},
    {0x0001, "未找到光斑 "},
    {0x0002, "不能移动到目标区域 "},
    {0x0004, "无法对称分布 "},
    {0x0008, "调节超时"},
    {0x0010, ""},
    {0x0020, ""},
    {0x0040, ""},
    {0x0080, ""},
};

//* process items
QMap<int, QString> IFaculaAdjust::mm_facula_judge_discribe = {
    {0x0000, " ok"},
    {0x0001, "光斑中心不在目标区域 "},
    {0x0002, "光斑十字不对称 "},
    {0x0004, "光斑外圈不对称 "},
    {0x0008, "中心光斑强度异常 "},
    {0x0010, "外圈光斑强度异常"},
    {0x0020, "光斑中心与十字区域强度差值异常"},
    {0x0040, ""},
    {0x0080, ""},
};

IFaculaAdjust::IFaculaAdjust() : mi_load_(new CLoadXml) {
    // 使用专门的配置提供者
    m_algorithmConfigProvider     = std::make_unique<Algorithm::AlgorithmConfigProvider>();
    m_adjustProcessConfigProvider = std::make_unique<AdjustProcess::AdjustProcessConfigProvider>();

    // 从算法配置提供者获取参数
    m_xml_param = m_algorithmConfigProvider->getAllParameters();

    // 记录算法参数加载日志
    qDebug() << "[INFO] Loaded algorithm parameters - find_origin_raduis:" << m_xml_param["find_origin_raduis"] << ", find_times:" << m_xml_param["find_times"]
             << ", peak_ok_threshold:" << m_xml_param["peak_ok_threshold"];

    qDebug() << "[INFO] Algorithm config - initial_dist(x:" << m_xml_param["initial_x_dist"] << ", y:" << m_xml_param["initial_y_dist"]
             << ", z:" << m_xml_param["initial_z_dist"] << "), z_move_step:" << m_xml_param["z_move_step"];

    // 如果配置为空，使用默认值
    if (m_xml_param.isEmpty()) {
        qDebug() << "[WARNING] Algorithm config is empty, using default values";
        loadLegacyXmlConfig();
    }

    //
    mm_symmetry_mp_param.insert(EFaculaJudgeMode::eFACULA_ADJUST,
                                StSymmetryInfo{
                                    C3dHandMachine::St3D<int16_t>{
                                        (int16_t)m_xml_param["ALR_mp_peak"],
                                        (int16_t)m_xml_param["AUD_mp_peak"],
                                        0,
                                    },
                                    C3dHandMachine::St3D<uint16_t>{
                                        (uint16_t)m_xml_param["ALR_mp_peak_threshold"],
                                        (uint16_t)m_xml_param["AUD_mp_peak_threshold"],
                                        0,
                                    },
                                });
    mm_symmetry_mp_param.insert(EFaculaJudgeMode::eFACULA_ADJUST_TEST,
                                StSymmetryInfo{
                                    C3dHandMachine::St3D<int16_t>{
                                        (int16_t)m_xml_param["FT_LRmp_adjust_peak"],
                                        (int16_t)m_xml_param["FT_UDmp_adjust_peak"],
                                        0,
                                    },
                                    C3dHandMachine::St3D<uint16_t>{
                                        (uint16_t)m_xml_param["FT_LRmp_adjust_peak_threshold"],
                                        (uint16_t)m_xml_param["FT_UDmp_adjust_peak_threshold"],
                                        0,
                                    },
                                });
    mm_symmetry_mp_param.insert(EFaculaJudgeMode::eFACULA_SOLID_TEST,
                                StSymmetryInfo{
                                    C3dHandMachine::St3D<int16_t>{
                                        (int16_t)m_xml_param["FT_LRmp_solid_peak"],
                                        (int16_t)m_xml_param["FT_UDmp_solid_peak"],
                                        0,
                                    },
                                    C3dHandMachine::St3D<uint16_t>{
                                        (uint16_t)m_xml_param["FT_LRmp_solid_peak_threshold"],
                                        (uint16_t)m_xml_param["FT_UDmp_solid_peak_threshold"],
                                        0,
                                    },
                                });
    mm_symmetry_mp_param.insert(EFaculaJudgeMode::eFACUAL_DEFLATE_TEST,
                                StSymmetryInfo{
                                    C3dHandMachine::St3D<int16_t>{
                                        (int16_t)m_xml_param["FT_LRmp_deflate_peak"],
                                        (int16_t)m_xml_param["FT_UDmp_deflate_peak"],
                                        0,
                                    },
                                    C3dHandMachine::St3D<uint16_t>{
                                        (uint16_t)m_xml_param["FT_LRmp_deflate_peak_threshold"],
                                        (uint16_t)m_xml_param["FT_UDmp_deflate_peak_threshold"],
                                        0,
                                    },
                                });
}

IFaculaAdjust::~IFaculaAdjust() {
    if (mi_load_ != nullptr)
        delete mi_load_;
}

/**
 * @brief 根据单帧 数据长度 切换接收数据类型
 * @param senssor 型号，
 * @return
 */
// bool IFaculaAdjust::cellNumUpdate(const IPhotonSensor::ESensorModel &sensor_model, StMapInfo *st_map_info_, StMapInfo *st_sub_map_info_)
//{
//    bool result = true;
//    uint8_t xlen, ylen;

//    if(IPhotonSensor::mm_mpNum_link_dimension.find(sensor_model) != IPhotonSensor::mm_mpNum_link_dimension.end()) {
//        /*1. 表格尺寸更新*/
//        st_map_info_->xlens = IPhotonSensor::mm_mpNum_link_dimension[sensor_model].x;
//        st_map_info_->ylens = IPhotonSensor::mm_mpNum_link_dimension[sensor_model].y;

//        st_map_info_->mp_order = IPhotonSensor::mm_mpNum_link_dimension[sensor_model].order;
//        st_map_info_->sensor_direction = IPhotonSensor::mm_mpNum_link_dimension[sensor_model].direct;
//    }
//    else {
//        qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-e IFaculaAdjust/ sensor model:" << sensor_model;
//        return false;
//    }

//    /*1.1 原表格配置*/
////    m_map_data_->map_matrix.resize(ylen); //

//    /*1.2 拓展表格配置*/
//    xlen += st_map_info_->xlens%2 == 0?1:2;
//    ylen += st_map_info_->ylens%2 == 0?1:2;

//    if(st_sub_map_info_ != nullptr) {
////        m_map_interpolation_data_->map_matrix.resize(ylen); //
////        for (uint i = 0; i < ylen; i++) {
////            m_map_interpolation_data_->map_matrix[i].resize(xlen);
////        }
//        st_sub_map_info_->xlens = xlen;
//        st_sub_map_info_->ylens = ylen;
//    }

//    return result;
//}

/**
 * @brief 加载旧的XML配置文件（向后兼容性）
 */
void IFaculaAdjust::loadLegacyXmlConfig() {
    qDebug() << "[INFO] Loading legacy XML configuration for algorithm parameters";

    // 设置默认值
    m_xml_param["initial_x_dist"] = 0;
    m_xml_param["initial_y_dist"] = 0;
    m_xml_param["initial_z_dist"] = 0;

    m_xml_param["find_origin_raduis"] = 140;
    m_xml_param["find_angle_step"]    = 20;
    m_xml_param["find_radius_step"]   = 140;

    m_xml_param["find_times"]        = 4;
    m_xml_param["discard_pack_num"]  = 1;
    m_xml_param["default_z_direct"]  = 1;
    m_xml_param["z_move_step"]       = 3;
    m_xml_param["peak_ok_threshold"] = 550;

    m_xml_param["Amp_select"]            = 30;
    m_xml_param["ALR_mp_peak"]           = 20;
    m_xml_param["ALR_mp_peak_threshold"] = 100;
    m_xml_param["AUD_mp_peak"]           = 0;
    m_xml_param["AUD_mp_peak_threshold"] = 100;

    m_xml_param["Aedge_peak_threshold"] = 180;
    m_xml_param["ACR_peak_delta"]       = 120;
    m_xml_param["ARR_peak_delta"]       = 50;
    m_xml_param["AMax_peak"]            = 650;

    m_xml_param["edge_peak_threshold"] = 50;
    m_xml_param["peak_threshold"]      = 600;
    m_xml_param["peak_max_threshold"]  = 1000;
    m_xml_param["CR_peak_delta"]       = 150;

    m_xml_param["FT_LRmp_adjust_peak"]           = 25;
    m_xml_param["FT_LRmp_adjust_peak_threshold"] = 5;
    m_xml_param["FT_UDmp_adjust_peak"]           = 0;
    m_xml_param["FT_UDmp_adjust_peak_threshold"] = 100;

    m_xml_param["FT_LRmp_solid_peak"]           = 25;
    m_xml_param["FT_LRmp_solid_peak_threshold"] = 8;
    m_xml_param["FT_UDmp_solid_peak"]           = 0;
    m_xml_param["FT_UDmp_solid_peak_threshold"] = 100;

    m_xml_param["FT_LRmp_deflate_peak"]           = 25;
    m_xml_param["FT_LRmp_deflate_peak_threshold"] = 8;
    m_xml_param["FT_UDmp_deflate_peak"]           = 0;
    m_xml_param["FT_UDmp_deflate_peak_threshold"] = 100;

    m_xml_param["Uaround_peak_threshold"] = 60;

    // 尝试从旧的XML文件加载
    QString filename = QApplication::applicationDirPath() + "/config/clen_config.xml";
    if (QFile::exists(filename)) {
        mi_load_->readParam(filename, &m_xml_param);
        qDebug() << "[INFO] Loaded legacy XML config from:" << filename;
    } else {
        qDebug() << "[WARNING] Legacy XML config file not found:" << filename;
    }
}
