#ifndef CONFIG_TYPES_H
#define CONFIG_TYPES_H

#include <QMap>
#include <QPoint>
#include <QString>
#include <QVariant>
#include <QVector>

namespace Config {

/**
 * @brief 配置文件类型枚举
 */
enum class FileType {
    System,    // 系统级配置
    Facula,    // 光斑调节配置
    Hardware,  // 硬件配置
    Algorithm  // 算法参数配置
};

/**
 * @brief 配置文件格式枚举
 */
enum class ConfigFormat {
    INI,   // INI格式
    XML,   // XML格式
    JSON,  // JSON格式
    Auto   // 自动检测格式
};

/**
 * @brief 模块配置信息
 */
struct ModuleConfigInfo {
    QString      moduleName;       // 模块名称
    QString      configPath;       // 配置路径
    QStringList  configFiles;      // 配置文件列表
    ConfigFormat preferredFormat;  // 首选格式

    ModuleConfigInfo() {
        preferredFormat = ConfigFormat::JSON;
    }
};

/**
 * @brief 系统配置数据结构 - 用于MES和版本管理
 */
struct SystemConfig {
    QString version;         // 版本信息
    QString userid;          // 用户ID
    QString op;              // 工序号
    QString work_number;     // 工单号
    QString work_domain;     // 工作域
    uint8_t station_number;  // 工位号

    SystemConfig() {
        version        = "2.6.1.6";
        userid         = "admin";
        op             = "105";
        work_number    = "10001";
        work_domain    = "001";
        station_number = 1;
    }
};

/**
 * @brief 设备配置数据结构 - 用于设备通信和控制
 */
struct DeviceConfig {
    uint8_t  sensor_device;        // 传感器设备
    uint32_t sensor_device_baud;   // 设备波特率
    uint8_t  clens_machine_brand;  // 镜片调节设备品牌

    DeviceConfig() {
        sensor_device       = 4;
        sensor_device_baud  = 230400;
        clens_machine_brand = 3;
    }
};

/**
 * @brief 流程配置数据结构 - 用于调节流程控制
 */
struct ProcessConfig {
    uint8_t  facula_ok_times;   // 判定次数
    uint32_t solid_time;        // 固化时间
    uint8_t  facula_ng_handle;  // 异常处理方式

    ProcessConfig() {
        facula_ok_times  = 3;
        solid_time       = 0;
        facula_ng_handle = 1;
    }
};

/**
 * @brief 光斑配置数据结构 - 专门用于光斑检测和处理
 */
struct FaculaConfig {
    // 多通道光斑中心配置
    QString         facula_center_channels;        // 多通道配置字符串
    QVector<QPoint> facula_center_points;          // 解析后的通道坐标列表
    uint32_t        facula_center_peak_threshold;  // 多通道模式下的peak阈值

    // 兼容性配置
    uint8_t facula_center_loc_x;  // 单点X坐标
    uint8_t facula_center_loc_y;  // 单点Y坐标

    // 光斑处理类型
    uint8_t facula_handle_type;  // 处理类型：0-基础，1-增强，2-高精度

    // 流程参数（为了兼容性保留，推荐通过getProcessConfig()访问）
    uint8_t  facula_ok_times;   // 判定次数
    uint32_t solid_time;        // 固化时间
    uint8_t  facula_ng_handle;  // 异常处理方式

    FaculaConfig() {
        facula_center_channels       = "2,2";
        facula_center_peak_threshold = 800;
        facula_center_loc_x          = 2;
        facula_center_loc_y          = 2;
        facula_handle_type           = 1;
        facula_ok_times              = 3;
        solid_time                   = 0;
        facula_ng_handle             = 1;

        // 初始化默认通道点
        facula_center_points.append(QPoint(2, 2));
    }
};

/**
 * @brief 硬件配置数据结构
 */
struct HardwareConfig {
    uint32_t xy_radius_limit;  // XY轴限位半径
    uint32_t z_radius_limit;   // Z轴限位
    uint8_t  x_step_dist;      // X单脉冲移动距离
    uint8_t  y_step_dist;      // Y单脉冲移动距离
    uint8_t  z_step_dist;      // Z单脉冲移动距离

    HardwareConfig() {
        xy_radius_limit = 1500;
        z_radius_limit  = 1400;
        x_step_dist     = 10;
        y_step_dist     = 10;
        z_step_dist     = 10;
    }
};

/**
 * @brief 算法配置数据结构
 */
struct AlgorithmConfig {
    // 传统算法参数映射（保持兼容性）
    QMap<QString, int> parameters;  // 算法参数映射

    // 图像处理算法参数（从FaculaConfig移过来）
    uint8_t interpolation_type;        // 插值类型: 0-双线性插值; 1-双三次插值
    QString filter_types;              // 滤波器类型组合，格式："1,2,3"
    float   interpolation_offset;      // 插值偏移量
    float   kalman_strength;           // 卡尔曼滤波强度
    uint8_t convolution_kernel_size;   // 卷积核大小
    QString convolution_preset;        // 卷积预设
    uint8_t median_kernel_size;        // 中值滤波核大小
    QString median_preset;             // 中值滤波预设
    float   gaussian_sigma;            // 高斯滤波标准差
    uint8_t gaussian_kernel_size;      // 高斯滤波核大小
    QString gaussian_preset;           // 高斯滤波预设
    float   bilateral_sigma_color;     // 双边滤波颜色标准差
    float   bilateral_sigma_space;     // 双边滤波空间标准差
    uint8_t bilateral_kernel_size;     // 双边滤波核大小
    QString bilateral_preset;          // 双边滤波预设
    uint8_t weighted_avg_kernel_size;  // 加权均值滤波核大小
    QString weighted_avg_preset;       // 加权均值滤波预设
    float   filter_strength;           // 全局滤波强度

    AlgorithmConfig() {
        // 初始化传统算法参数
        parameters["initial_x_dist"]        = 0;
        parameters["initial_y_dist"]        = 0;
        parameters["initial_z_dist"]        = 0;
        parameters["find_origin_raduis"]    = 140;
        parameters["find_angle_step"]       = 20;
        parameters["find_radius_step"]      = 140;
        parameters["find_times"]            = 4;
        parameters["discard_pack_num"]      = 1;
        parameters["default_z_direct"]      = 1;
        parameters["z_move_step"]           = 3;
        parameters["peak_ok_threshold"]     = 550;
        parameters["Amp_select"]            = 30;
        parameters["ALR_mp_peak"]           = 20;
        parameters["ALR_mp_peak_threshold"] = 100;
        parameters["AUD_mp_peak"]           = 0;
        parameters["AUD_mp_peak_threshold"] = 100;
        parameters["Aedge_peak_threshold"]  = 180;
        parameters["ACR_peak_delta"]        = 120;
        parameters["ARR_peak_delta"]        = 50;
        parameters["AMax_peak"]             = 650;
        parameters["edge_peak_threshold"]   = 50;
        parameters["peak_threshold"]        = 600;
        parameters["peak_max_threshold"]    = 1000;
        parameters["CR_peak_delta"]         = 150;
        // 功能测试参数
        parameters["FT_LRmp_adjust_peak"]            = 25;
        parameters["FT_LRmp_adjust_peak_threshold"]  = 5;
        parameters["FT_UDmp_adjust_peak"]            = 0;
        parameters["FT_UDmp_adjust_peak_threshold"]  = 100;
        parameters["FT_LRmp_solid_peak"]             = 25;
        parameters["FT_LRmp_solid_peak_threshold"]   = 8;
        parameters["FT_UDmp_solid_peak"]             = 0;
        parameters["FT_UDmp_solid_peak_threshold"]   = 100;
        parameters["FT_LRmp_deflate_peak"]           = 25;
        parameters["FT_LRmp_deflate_peak_threshold"] = 8;
        parameters["FT_UDmp_deflate_peak"]           = 0;
        parameters["FT_UDmp_deflate_peak_threshold"] = 100;

        // 初始化图像处理算法参数
        interpolation_type       = 0;
        filter_types             = "6";
        interpolation_offset     = 0.5f;
        kalman_strength          = 1.0f;
        convolution_kernel_size  = 3;
        convolution_preset       = "sharpen";
        median_kernel_size       = 3;
        median_preset            = "noise_reduction";
        gaussian_sigma           = 1.0f;
        gaussian_kernel_size     = 5;
        gaussian_preset          = "medium_blur";
        bilateral_sigma_color    = 75.0f;
        bilateral_sigma_space    = 75.0f;
        bilateral_kernel_size    = 5;
        bilateral_preset         = "smooth";
        weighted_avg_kernel_size = 3;
        weighted_avg_preset      = "center_weighted";
        filter_strength          = 1.0f;
    }
};

/**
 * @brief 配置错误类型
 */
enum class ErrorType { None, FileNotFound, ParseError, ValidationError, PermissionError, UnknownError };

/**
 * @brief 配置结果结构
 */
struct ConfigResult {
    bool      success;
    ErrorType error;
    QString   message;

    ConfigResult(bool s = true, ErrorType e = ErrorType::None, const QString &m = QString()) : success(s), error(e), message(m) {
    }
};

}  // namespace Config

#endif  // CONFIG_TYPES_H
