# 光路调节本配置重构方案

原配置文件：
path：bin/config/

- clen_config.ini
- clen_config.xml
- clen_config_QH_SHJ.xml

加载配置代码：lensReadIni.cpp/h

使用配置代码：

- clenAdjustOperation.cpp 光路调节功能参数
- faculaCircle.cpp 加载光斑调节参数
- 

## 重构目标

- 配置管理独立模块 components/config
- 光路配置参数根据职责重新划分，根据参数的类型，使用的位置从新划分。
例如下面这些参数，报错算法配置的参数，都需要重新调整
          // 多通道光斑中心配置
        QString         facula_center_channels;        // 多通道配置字符串，格式："x1,y1;x2,y2;x3,y3"
        QVector<QPoint> facula_center_points;          // 解析后的通道坐标列表
        uint32_t        facula_center_peak_threshold;  // 多通道模式下的peak阈值

        // 兼容性：保留原有单点配置（从多通道配置中取第一个点）
        uint8_t facula_center_loc_x;
        uint8_t facula_center_loc_y;


        uint8_t facula_handle_type;  // 0-原光斑调节; 1-启用处理后光斑调节
- 参数的格式也可以调整，不一定要用xml文档和ini文档，也可以用json，选择合适的类型

## 编译 & 测试

build type: Debug

cmake: 
[proc] Executing command: D:\programs\CMake\bin\cmake.exe -DCMAKE_BUILD_TYPE:STRING=Debug -DCMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE -DCMAKE_C_COMPILER:FILEPATH=D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-gcc.exe -DCMAKE_CXX_COMPILER:FILEPATH=D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe -DCMAKE_EXPORT_COMPILE_COMMANDS=ON --no-warn-unused-cli -S F:/13_Yapha-Laser-DTof2dMS/development/tool/LA-T5 -B f:/13_Yapha-Laser-DTof2dMS/development/tool/LA-T5/build/Debug -G Ninja

编译配置：
    "cmake.buildDirectory": "${workspaceFolder}/build/${buildType}",
    "cmake.configureOnOpen": true,
    "cmake.generator": "Ninja",
    "cmake.configureArgs": [
        "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
    ],

日志检查：


## 输出

[[TOF接收镜片光路耦合软件使用文档]]
