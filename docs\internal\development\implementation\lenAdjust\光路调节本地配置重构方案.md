# 光路调节本地配置重构方案

## 项目架构分析

当前项目采用模块化架构，光路调节功能是项目中的一个子功能模块（类似插件架构）。配置管理模块(`components/config`)是整个项目的基础设施，为所有功能模块提供统一的配置管理服务。

### 原配置文件
- **位置**: bin/config/
- **文件**: clen_config.ini, clen_config.xml, clen_config_QH_SHJ.xml
- **加载代码**: lensReadIni.cpp/h
- **使用代码**:
  - clenAdjustOperation.cpp - 光路调节功能参数
  - faculaCircle.cpp - 光斑调节参数
  - photonSensor目录下的相关代码

## 重构目标

### 1. 配置管理架构优化
- **模块化配置管理**: 配置管理模块支持多功能模块的配置
- **统一配置接口**: 所有模块通过统一接口访问配置
- **配置目录结构**: 按功能模块组织配置文件

### 2. 配置参数职责重新划分
将混合在一起的配置参数按职责重新分类：

**光斑调节配置 (FaculaConfig)**:
```cpp
// 多通道光斑中心配置
QString         facula_center_channels;        // 多通道配置字符串，格式："x1,y1;x2,y2;x3,y3"
QVector<QPoint> facula_center_points;          // 解析后的通道坐标列表
uint32_t        facula_center_peak_threshold;  // 多通道模式下的peak阈值

// 兼容性：保留原有单点配置
uint8_t facula_center_loc_x;
uint8_t facula_center_loc_y;

// 调节参数
uint8_t facula_ok_times;                       // 判定次数
uint32_t solid_time;                           // 固化时间
uint8_t facula_ng_handle;                      // 异常处理方式
uint8_t facula_handle_type;                    // 处理类型
```

**算法配置 (AlgorithmConfig)** - 从FaculaConfig中分离出来:
```cpp
// 图像处理算法参数
uint8_t interpolation_type;                    // 插值类型
float interpolation_offset;                    // 插值偏移
QString filter_types;                          // 滤波器类型
float filter_strength;                         // 滤波强度
float kalman_strength;                         // 卡尔曼滤波强度
// ... 其他算法参数
```

### 3. 配置文件格式统一
- **格式选择**: 采用JSON格式，提高可读性和扩展性
- **向后兼容**: 支持从INI/XML格式自动迁移
- **配置验证**: 增强配置文件格式和内容验证

### 4. 模块化目录结构
```
config/
├── system/                          # 整个项目的系统级配置
│   └── system_config.json
├── modules/                         # 各功能模块的配置
│   ├── lenAdjust/                   # 光路调节模块配置
│   │   ├── facula_config.json      # 光斑调节配置
│   │   ├── hardware_config.json    # 硬件配置
│   │   └── algorithm_config.json   # 算法参数配置
│   └── [otherModule]/               # 其他功能模块配置
├── templates/                       # 配置模板
│   ├── lenAdjust/
│   │   ├── facula_config_template.json
│   │   ├── hardware_config_template.json
│   │   └── algorithm_config_template.json
│   └── system_config_template.json
└── backup/                          # 配置备份
    └── [timestamp]/
        ├── system_config.json.bak
        └── modules/
```

### 5. 代码架构重构
- **光路调节模块化**: 整合components/lensAdjust和photonSensor相关代码
- **配置访问统一**: 通过ConfigManager统一访问配置
- **模块解耦**: 实现模块间的松耦合设计

## 实施计划

### 阶段1：配置管理模块增强

1. **更新配置数据结构**
   - 修改 `ConfigTypes.h`，支持模块化配置
   - 在 `ConfigManager` 中添加模块配置支持
   - 实现配置目录的模块化组织

2. **添加JSON格式支持**
   - 在 `ConfigManager` 中添加JSON格式读写
   - 实现配置格式自动检测和转换
   - 保持向后兼容性

### 阶段2：光路调节模块配置重构

1. **配置参数重新分类**
   - 将算法参数从 `FaculaConfig` 移到 `AlgorithmConfig`
   - 创建 lenAdjust 模块的配置文件结构
   - 实现配置参数的验证和默认值

2. **配置迁移功能**
   - 实现从旧配置文件的自动迁移
   - 提供配置备份和回滚机制
   - 记录迁移日志和错误处理

### 阶段3：光路调节代码架构重构

1. **配置访问接口统一**
   - 重构 `lensReadIni.cpp/h`，使用新的配置管理接口
   - 更新 `clenAdjustOperation.cpp` 中的配置访问方式
   - 重构 `photonSensor` 目录下的相关代码

2. **模块化架构优化**
   - 整合光路调节相关的代码模块
   - 实现模块间的松耦合设计
   - 符合单一职责和开闭原则

### 阶段4：配置文档和架构图更新

1. **文档完善**
   - 更新配置使用指南
   - 创建模块化架构图和类图
   - 编写配置迁移指南

2. **示例和模板**
   - 创建配置文件示例
   - 提供配置模板文件
   - 编写最佳实践文档

### 阶段5：测试和验证

1. **单元测试**
   - 编写配置管理模块的单元测试
   - 测试配置迁移功能
   - 验证模块化架构的正确性

2. **集成测试**
   - 测试光路调节功能的完整流程
   - 验证配置热重载功能
   - 性能测试和稳定性测试

## 风险控制

### 向后兼容性
- 保持对现有配置文件格式的支持
- 提供自动配置迁移功能
- 保留旧配置文件作为备份

### 渐进式重构
- 分阶段实施，每个阶段都可以独立工作
- 保持系统在重构过程中的可用性
- 提供配置回滚机制

### 错误处理
- 完善的配置验证和错误提示
- 详细的日志记录和调试信息
- 异常情况下的降级处理

## 编译 & 测试

### 构建配置
- **Build Type**: Debug
- **CMake Generator**: Ninja
- **编译器**: MinGW 7.3.0 (Qt 5.14.2)

### CMake配置
```bash
cmake -DCMAKE_BUILD_TYPE:STRING=Debug \
      -DCMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE \
      -DCMAKE_C_COMPILER:FILEPATH=D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-gcc.exe \
      -DCMAKE_CXX_COMPILER:FILEPATH=D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe \
      -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
      -S F:/13_Yapha-Laser-DTof2dMS/development/tool/LA-T5 \
      -B f:/13_Yapha-Laser-DTof2dMS/development/tool/LA-T5/build/Debug \
      -G Ninja
```

### 验证检查
- **配置加载验证**: 检查所有配置参数是否正确加载
- **日志检查**: 查看配置管理相关的日志输出
- **功能测试**: 验证光路调节功能是否正常工作
- **性能测试**: 确保重构后性能没有显著下降

## 预期输出

### 配置文件结构
- 模块化的配置目录结构
- JSON格式的配置文件
- 完整的配置模板和示例

### 代码架构
- 统一的配置管理接口
- 模块化的光路调节功能
- 清晰的职责分离和依赖关系

### 文档输出
- [[TOF接收镜片光路耦合软件使用文档]]
- 配置管理开发文档
- 模块化架构设计文档
