# 光路调节配置文件使用指南

## 📖 概述

本指南介绍如何使用和配置光路调节系统的配置文件。新的配置系统采用JSON格式，提供了更好的可读性和扩展性。

## 🗂️ 配置文件结构

```
config/
├── system/
│   └── system_config.json          # 系统全局配置
└── modules/
    └── lenAdjust/
        ├── facula_config.json       # 光斑配置
        ├── algorithm_config.json    # 算法配置
        └── hardware_config.json     # 硬件配置
```

## 🔧 配置文件类型说明

### 1. 光斑配置 (facula_config.json)

**用途**: 配置光斑检测和处理相关参数

**主要参数**:
- `facula_center_channels`: 多通道光斑位置坐标
- `facula_center_peak_threshold`: 光斑峰值检测阈值
- `facula_handle_type`: 光斑处理算法类型

### 2. 算法配置 (algorithm_config.json)

**用途**: 配置传统算法参数和图像处理算法

**主要参数**:
- `initial_x_dist`, `initial_y_dist`, `initial_z_dist`: 初始移动距离
- `find_origin_raduis`: 寻找原点的搜索半径
- `peak_ok_threshold`: 峰值OK阈值

### 3. 硬件配置 (hardware_config.json)

**用途**: 配置硬件设备参数和运动控制

**主要参数**:
- `xy_radius_limit`, `z_radius_limit`: 运动半径限制
- `x_step_dist`, `y_step_dist`, `z_step_dist`: 单脉冲移动距离

## 💻 推荐编辑软件

### 1. Visual Studio Code (推荐)
- ✅ 免费开源
- ✅ 优秀的JSON语法高亮
- ✅ 自动格式化和错误检查
- ✅ 支持注释和智能提示

### 2. Notepad++
- ✅ 轻量级
- ✅ JSON语法高亮
- ✅ 免费

### 3. Sublime Text
- ✅ 强大的编辑功能
- ✅ 优秀的JSON支持

### ❌ 不推荐使用
- Windows记事本 (无语法高亮)
- Word等文档编辑器 (可能破坏格式)

## 🎯 参数配置指南

### 参数标记说明

配置文件中使用以下标记来区分参数类型：

- `🔧 [用户配置]`: 需要根据实际情况调整的参数
- `📖 [系统信息]`: 仅供参考，通常不需要修改的参数

### 光斑检测参数调优

#### 1. 光斑位置配置
```json
"facula_center_channels": "2,2;1,2;3,2"
```
- **格式**: `x1,y1;x2,y2;x3,y3`
- **调整方法**: 使用图像查看工具确定光斑实际位置
- **注意事项**: 坐标以图像左上角为原点(0,0)

#### 2. 峰值阈值配置
```json
"facula_center_peak_threshold": 800
```
- **范围**: 100-2000
- **调整原则**: 
  - 光斑太弱时降低此值
  - 光斑过强时提高此值
  - 从默认值800开始微调

#### 3. 算法类型选择
```json
"facula_handle_type": 1
```
- **选项**: 
  - `0`: 基础算法 (速度快)
  - `1`: 增强算法 (推荐)
  - `2`: 高精度算法 (精度高，速度慢)

## 🚀 配置部署流程

### 1. 开发环境配置
项目中的配置文件位于源码目录的 `config/` 文件夹中，这些是模板文件。

### 2. 运行时配置
应用程序启动时会自动将配置文件复制到执行文件目录下的 `config/` 文件夹。

### 3. 配置更新策略
- **首次运行**: 自动复制所有配置文件
- **后续运行**: 跳过已存在的配置文件（保护用户修改）
- **强制更新**: 可通过配置工具强制更新

## 🧪 配置测试

### 使用配置测试工具

1. **编译测试工具**:
```bash
cd tools
g++ -I../components/config config_test_app.cpp -o config_test
```

2. **部署配置文件**:
```bash
./config_test --deploy --verbose
```

3. **运行配置测试**:
```bash
./config_test --test --verbose
```

4. **完整测试**:
```bash
./config_test --deploy --test --verbose
```

### 测试项目包括
- ✅ 配置文件加载测试
- ✅ 配置参数验证测试
- ✅ 向后兼容性测试
- ✅ 配置性能测试
- ✅ 配置集成测试

## ⚠️ 注意事项

### 1. 文件格式
- 必须使用UTF-8编码
- 严格遵循JSON格式
- 注释使用 `"//": "注释内容"` 格式

### 2. 参数范围
- 所有数值参数都有有效范围
- 超出范围的值可能导致系统异常
- 参考配置文件中的 `_validation_rules` 部分

### 3. 备份建议
- 修改配置前建议备份原文件
- 系统会自动创建备份（.backup_时间戳）
- 重要配置建议版本控制

### 4. 生效时机
- 配置修改后需要重启应用程序
- 部分参数支持热重载（未来版本）

## 🔧 故障排除

### 常见问题

1. **配置文件无法加载**
   - 检查JSON格式是否正确
   - 确认文件编码为UTF-8
   - 使用配置测试工具验证

2. **参数不生效**
   - 确认已重启应用程序
   - 检查参数值是否在有效范围内
   - 查看应用程序日志

3. **配置文件丢失**
   - 运行配置部署工具
   - 从备份文件恢复
   - 重新生成默认配置

### 获取帮助

如果遇到问题，请：
1. 查看应用程序日志
2. 运行配置测试工具
3. 联系技术支持

## 📚 相关文档

- [配置系统架构设计](配置系统架构设计.md)
- [API参考文档](API参考文档.md)
- [故障排除指南](故障排除指南.md)
