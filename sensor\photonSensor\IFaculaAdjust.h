#ifndef _IFaculaAdjust_ADJUST_H_
#define _IFaculaAdjust_ADJUST_H_

#include "loadXml.h"
#include <QByteArray>


//#include <QTableWidgetItem>

#include "3dHandMachine.h"
#include "ILaser.h"
#include "IPhotonSensor.h"  //


class IFaculaAdjust {
  public:
    IFaculaAdjust();
    virtual ~IFaculaAdjust();

    enum EFaculaStatus {
        eFATAL = -2,  //
        eERROR = -1,
        eWAIT  = 0,
        eOK    = 1,  //
    };

    //* 对称调节方式
    //    enum EFaculaSysType {

    //    };


    //    <Amp_select>02</Amp_select>
    //    <!--  十字光斑特定通道调节 7bit:0-小于, 1-大于; L-0x01, R-0x02, U-0x04, D-0x08  -->
    //    <Amp_L>100</Amp_L>
    //    <!--    -->
    //    <Amp_R>100</Amp_R>
    //    <!--    -->
    //    <Amp_U>100</Amp_U>
    //    <!--    -->
    //    <Amp_D>100</Amp_D>
    typedef struct {
        uint8_t sys_type;
        struct step {
            uint8_t mp_l;
            uint8_t mp_r;
            uint8_t mp_u;
            uint8_t mp_d;
            uint8_t reserve1;
            uint8_t reserve2;
            uint8_t reserve3;
            uint8_t mp_adjust_direct;
        };
    } StSymmetry;

    // 主要步骤
    //    enum class EFaculaMainStep {
    //        eFACULA_ADJUST,  //调节
    //        eFACULA_TEST,    //判定
    //    };

    enum EFaculaJudgeMode {
        eFACULA_ADJUST,
        eFACULA_ADJUST_TEST,
        eFACULA_SOLID_TEST,
        eFACUAL_DEFLATE_TEST,

        //        eFACULA_TEST,
    };

    //* 对称调节方式
    enum ESymmetryAdjustType {
        eFACULA_FOCUS = 0,  //聚焦调节(光斑聚焦)
        eDESIGNATE_MP = 1,  //虚光斑调节(光斑均匀)
    };

    //
    enum EDesignateMpType {
        eMP_CENTER_RATIO          = 0,  //通道/中心比值
        eSYMMETRY_MP_RATIO        = 1,  //对称比值
        eMP_DESIGNATE_VALUE       = 2,  //通道固定值
        eMP_DESIGNATE_DELTA_VALUE = 3,
    };

    // 固定的通道
    enum EDesignateMp {
        eMP_LEFT  = 0x01,
        eMP_RIGHT = 0x02,
        eMP_UP    = 0x04,
        eMP_DOWN  = 0x08,
    };

    //* process items
    typedef union {
        uint16_t adjust_step;
        struct adjust_step {
            uint16_t facula_find : 1;            // find facula
            uint16_t facula_target : 1;          // facula center in target
            uint16_t facula_symmetry : 1;        //中间十字对称
            uint16_t facula_round_sym : 1;       //外圈对称
            uint16_t facula_adjust_timeout : 1;  //
            uint16_t retest : 1;                 //
        } all_step;
    } UFaculaAdjustDds;

    typedef union {
        uint16_t dds_info;
        struct {
            uint16_t facula_target : 1;     // facula center in target
            uint16_t facula_symmetry : 1;   //中间十字对称
            uint16_t facula_round_sym : 1;  //外圈对称
            uint16_t center_peak_down : 1;  //中间数据最小阈值
            uint16_t round_peak_down : 1;   //周围数值最小阈值
            uint16_t CR_peak_delta : 1;     //中间和四周peak差值
        } all_info;
    } UFaculaJudgeDds;

    //* 3.1单通道数据
    typedef struct {
        uint32_t peak;
        int8_t   ax;  //坐标x
        int8_t   ay;  //坐标y
    } StSingleMp;

    //* 3.2全通道灰度图
    typedef struct {
        uint32_t                   max_peak;
        uint32_t                   peak_second;  //第二高值
        StSingleMp                 max_mp;       //最大通道
        QVector<QVector<uint32_t>> map_matrix;   // MP matrix 数据, 左上角（0，0），横x轴，纵Y轴，X->Y，顺时针
    } StMapData;                                 //

    typedef struct {
        uint8_t                    xlens;
        uint8_t                    ylens;
        StSingleMp                 target_tf;                     //目标MP TF (主要目标，兼容性)
        QVector<StSingleMp>        target_channels;               //多通道目标列表
        uint32_t                   multi_channel_peak_threshold;  //多通道模式下的peak阈值
        bool                       sensor_direction;              //贴片方向
        IPhotonSensor::EFaculaForm facula_form;
        ESymmetryAdjustType        symm_type;
        //        QVector<QTableWidgetItem*>           table_item;
    } StMapInfo;  //

    //    typedef struct {
    //        bool                                 sensor_direction; //贴片方向
    //        StSingleMp                           target_tf; //目标MP TF
    //    } StMapTargetInfo; //目标光斑信息

    static QMap<int, QString> mm_adjust_step_discribe;
    static QMap<int, QString> mm_facula_judge_discribe;

    //    //* 调节数据
    //    static bool greymapInfoUpdate(const IPhotonSensor::ESensorModel &sensor_model, StMapInfo *st_map_info_, StMapInfo *st_sub_map_info_) {
    //        bool result = true;

    //        if(IPhotonSensor::sm_mpNum_link_dimension.find(sensor_model) != IPhotonSensor::sm_mpNum_link_dimension.end()) {
    //            /*1. 表格尺寸更新*/
    //            st_map_info_->xlens = IPhotonSensor::sm_mpNum_link_dimension[sensor_model].x;
    //            st_map_info_->ylens = IPhotonSensor::sm_mpNum_link_dimension[sensor_model].y;

    //            st_map_info_->mp_order = IPhotonSensor::sm_mpNum_link_dimension[sensor_model].order;
    ////            st_map_info_->sensor_direction = IPhotonSensor::sm_mpNum_link_dimension[sensor_model].direct;

    //            st_map_info_->table_item.resize(0);
    //        }
    //        else return false;

    //        /*1.1 原表格配置*/
    //        //    m_map_data_->map_matrix.resize(ylen); //

    //        /*1.2 拓展表格配置*/
    //        if(st_sub_map_info_ != nullptr) {
    //            st_sub_map_info_->xlens = st_map_info_->xlens + (st_map_info_->xlens%2 == 0?1:2);
    //            st_sub_map_info_->ylens = st_map_info_->ylens + (st_map_info_->ylens%2 == 0?1:2);

    //            st_sub_map_info_->target_tf.ax = st_map_info_->target_tf.ax + 1;
    ////            st_sub_map_info_->target_tf.ax =  (st_sub_map_info_->target_tf.ax >=
    /// st_sub_map_info_->xlens)?st_map_info_->target_tf.ax:st_sub_map_info_->target_tf.ax;
    //            st_sub_map_info_->target_tf.ay = st_map_info_->target_tf.ay + 1;
    ////            st_sub_map_info_->target_tf.ay = st_sub_map_info_->target_tf.ay >=
    /// st_sub_map_info_->ylens?st_map_info_->target_tf.ay:st_sub_map_info_->target_tf.ay;

    //            st_sub_map_info_->table_item.resize(0);
    //        }

    //        return result;
    //    }

    //    static void greyMapShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_, const IFaculaAdjust::StMapData &map_data)
    //    {
    //        uint8_t alpha_tmp = 180, rgb_tmp;
    //        uint32_t data_tmp = 0;

    //        uint8_t xlens = map_info_->xlens;
    //        uint8_t ylens = map_info_->ylens;
    //        uint32_t data_max = map_data.max_peak>10?map_data.max_peak:10; //peak 最小值

    //        for (int y = 0; y < ylens; ++y)
    //        {
    //            for (int x = 0; x < xlens; ++x)
    //            {
    //                /*1. 数值 update*/
    //                data_tmp = map_data.map_matrix.at(y).at(x);
    //                map_info_->table_item.at(y*xlens + x)->setText(QString::number(data_tmp));

    //                /*3. 背景颜色*/
    //                QColor color;
    //                rgb_tmp = 255 - (data_tmp * 255 / data_max);
    //                alpha_tmp = data_tmp * 255 / data_max;
    //                color.setRgb(rgb_tmp, rgb_tmp, rgb_tmp, alpha_tmp);
    //                map->item(y,x)->setBackground(QBrush(color));
    //            }
    //        }
    //    }

    virtual void variblesInit(void) = 0;

    virtual bool faculaMp(StMapData *map_data_) = 0;

    virtual uint8_t faculaAdjust(StMapData *map_data_, const C3dHandMachine::St3D<int16_t> &move_delta_step, C3dHandMachine::St3D<int16_t> *move_dis_) = 0;
    //    virtual uint8_t faculaAdjustT(StMapData *map_data_,
    //                         const C3dHandMachine::St3D<int16_t> &move_delta_step, C3dHandMachine::St3D<int16_t> *move_dis_) = 0;

    virtual uint16_t faculaTest(StMapData *map_data_, QVector<uint32_t> &target_facula, const EFaculaJudgeMode &test_mode, const bool &has_adjusted) = 0;

    virtual void findMaxMP(StMapData *map_data_) = 0;

  protected:
    QMap<QString, int> m_xml_param;
    StSymmetry         mst_symmetry;

    struct StSymmetryInfo {
        C3dHandMachine::St3D<int16_t>  peak_value;  //通道比例或固定值或固定差值
        C3dHandMachine::St3D<uint16_t> peak_threshold_value;
    };

    QMap<EFaculaJudgeMode, StSymmetryInfo> mm_symmetry_mp_param;  //对称调节，通道差值。可以是：对称通道比较，固定通道比较

  private:
    CLoadXml *mi_load_;
};


#endif
