{"_comment": "光斑配置文件 - 光路调节模块", "_version": "1.0", "_description": "光斑检测和调节参数配置", "_last_updated": "2024-01-01T00:00:00Z", "facula_center": {"_comment": "多通道光斑中心配置", "facula_center_channels": "2,2;1,2;3,2", "facula_center_peak_threshold": 800, "_field_descriptions": {"facula_center_channels": "多通道配置字符串，格式：x1,y1;x2,y2;x3,y3。每个坐标对应一个检测通道的位置", "facula_center_peak_threshold": "多通道模式下的峰值阈值，用于判断光斑是否有效"}, "_examples": {"single_channel": "2,2", "dual_channel": "2,2;3,2", "triple_channel": "2,2;1,2;3,2", "quad_channel": "1,1;1,3;3,1;3,3"}, "_coordinate_range": {"x_min": 0, "x_max": 4, "y_min": 0, "y_max": 4, "note": "坐标范围基于5x5传感器阵列"}}, "adjust_param": {"_comment": "光斑调节参数", "facula_ok_times": 3, "solid_time": 5000, "facula_ng_handle": 0, "facula_handle_type": 0, "_field_descriptions": {"facula_ok_times": "连续成功检测次数要求，达到此次数才认为调节成功", "solid_time": "固化时间（毫秒），调节完成后的稳定等待时间", "facula_ng_handle": "异常处理方式：0-手动处理，1-自动继续执行", "facula_handle_type": "处理类型：0-原始光斑调节，1-启用处理后光斑调节"}, "_value_ranges": {"facula_ok_times": {"min": 1, "max": 10, "recommended": 3, "note": "建议值3-5次，过低可能不稳定，过高影响效率"}, "solid_time": {"min": 0, "max": 30000, "unit": "milliseconds", "note": "0表示无固化时间，通常设置1000-10000ms"}}}, "compatibility": {"_comment": "向后兼容性配置（自动从多通道配置中提取）", "_note": "这些字段由系统自动计算，无需手动配置", "facula_center_loc_x": 2, "facula_center_loc_y": 2, "_auto_generated": true}, "_migration_info": {"_comment": "配置迁移信息", "migrated_from": "clen_config.ini", "migration_date": "auto-generated", "original_sections": ["ADJUST_PARAM", "FACULA_HANDLE"], "separated_from": "原FaculaConfig中分离出算法参数到algorithm_config.json"}, "_usage_notes": {"multi_channel_setup": "多通道配置时，系统会同时监控所有指定位置的光斑强度", "threshold_tuning": "peak_threshold需要根据实际光斑强度调整，过低会误检，过高会漏检", "coordinate_system": "坐标系以传感器阵列左上角为(0,0)，向右向下递增", "performance_impact": "通道数量影响检测速度，建议根据实际需求选择合适的通道数"}}