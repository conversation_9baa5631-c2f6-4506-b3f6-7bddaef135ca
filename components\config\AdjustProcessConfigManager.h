#pragma once

#include "IModuleConfigManager.h"
#include "ConfigTypes.h"
#include <QObject>
#include <QFileSystemWatcher>

namespace Config {

/**
 * @brief 调节流程配置管理器
 * 
 * 专门负责光路调节流程控制相关的配置管理
 * 职责：
 * - 管理光斑判定次数配置
 * - 管理固化时间配置
 * - 管理异常处理方式配置
 */
class AdjustProcessConfigManager : public QObject, public IModuleConfigManager {
    Q_OBJECT

public:
    explicit AdjustProcessConfigManager(QObject *parent = nullptr);
    ~AdjustProcessConfigManager() override;

    // IModuleConfigManager 接口实现
    ConfigResult loadConfig() override;
    ConfigResult saveConfig() override;
    bool isConfigValid() const override;
    QString getConfigFilePath() const override;
    QString getModuleName() const override { return "AdjustProcess"; }

    /**
     * @brief 获取调节流程配置
     * @return 调节流程配置引用
     */
    const AdjustProcessConfig &getConfig() const { return m_config; }

    /**
     * @brief 设置调节流程配置
     * @param config 新的调节流程配置
     */
    void setConfig(const AdjustProcessConfig &config);

    /**
     * @brief 获取光斑判定次数
     * @return 判定次数
     */
    uint8_t getFaculaOkTimes() const {
        return m_config.facula_ok_times;
    }

    /**
     * @brief 设置光斑判定次数
     * @param times 判定次数
     */
    void setFaculaOkTimes(uint8_t times);

    /**
     * @brief 获取固化时间
     * @return 固化时间(毫秒)
     */
    uint32_t getSolidTime() const {
        return m_config.solid_time;
    }

    /**
     * @brief 设置固化时间
     * @param time 固化时间(毫秒)
     */
    void setSolidTime(uint32_t time);

    /**
     * @brief 获取异常处理方式
     * @return 异常处理方式
     */
    uint8_t getFaculaNgHandle() const {
        return m_config.facula_ng_handle;
    }

    /**
     * @brief 设置异常处理方式
     * @param handle 异常处理方式：0-停止，1-继续，2-重试
     */
    void setFaculaNgHandle(uint8_t handle);

Q_SIGNALS:
    /**
     * @brief 配置变更信号
     */
    void configChanged();

private Q_SLOTS:
    /**
     * @brief 配置文件变更处理
     * @param path 文件路径
     */
    void onFileChanged(const QString &path);

private:
    /**
     * @brief 验证配置参数
     * @return 验证结果
     */
    ConfigResult validateConfig() const;

    /**
     * @brief 生成默认配置
     */
    void generateDefaultConfig();

private:
    AdjustProcessConfig m_config;           // 调节流程配置数据
    QFileSystemWatcher *m_fileWatcher;      // 文件监控器
    QString m_configFilePath;               // 配置文件路径
};

} // namespace Config

#include "AdjustProcessConfigManager.moc"
