#include "ConfigManager.h"
#include <QApplication>
#include <QDateTime>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QMutexLocker>
#include <QTextStream>
#include <QXmlStreamReader>
#include <QXmlStreamWriter>

namespace Config {

ConfigManager *ConfigManager::s_instance = nullptr;
QMutex         ConfigManager::s_mutex;

ConfigManager *ConfigManager::getInstance() {
    QMutexLocker locker(&s_mutex);
    if (!s_instance) {
        s_instance = new ConfigManager();
    }
    return s_instance;
}

ConfigManager::ConfigManager(QObject *parent) : QObject(parent), m_fileWatcher(new QFileSystemWatcher(this)) {
    // 设置文件监控
    setupFileWatcher();

    // 连接信号
    connect(m_fileWatcher, &QFileSystemWatcher::fileChanged, this, &ConfigManager::onConfigFileChanged);

    logInfo("ConfigManager initialized");
}

ConfigManager::~ConfigManager() {
    saveAllConfigs();
}

ConfigResult ConfigManager::loadAllConfigs() {
    logInfo("Loading all configuration files...");

    // 确保配置目录存在
    if (!ensureConfigDirectory()) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to create config directory");
    }

    // 检查是否需要从旧配置迁移
    ConfigResult migrationResult = migrateFromLegacyConfig();
    if (!migrationResult.success) {
        logError("Failed to migrate from legacy configuration: " + migrationResult.message);
        return migrationResult;
    }

    // 生成缺失的配置文件
    ConfigResult genResult = generateDefaultConfigs();
    if (!genResult.success) {
        logError("Failed to generate default configurations: " + genResult.message);
        return genResult;
    }

    // 加载各个配置文件
    ConfigResult result;

    result = loadConfig(FileType::System);
    if (!result.success) {
        logError("Failed to load system config: " + result.message);
        return result;
    }

    result = loadConfig(FileType::Facula);
    if (!result.success) {
        logError("Failed to load facula config: " + result.message);
        return result;
    }

    result = loadConfig(FileType::Hardware);
    if (!result.success) {
        logError("Failed to load hardware config: " + result.message);
        return result;
    }

    result = loadConfig(FileType::Algorithm);
    if (!result.success) {
        logError("Failed to load algorithm config: " + result.message);
        return result;
    }

    logInfo("All configuration files loaded successfully");
    return ConfigResult(true);
}

ConfigResult ConfigManager::saveAllConfigs() {
    logInfo("Saving all configuration files...");

    ConfigResult result;

    result = saveConfig(FileType::System);
    if (!result.success)
        return result;

    result = saveConfig(FileType::Facula);
    if (!result.success)
        return result;

    result = saveConfig(FileType::Hardware);
    if (!result.success)
        return result;

    result = saveConfig(FileType::Algorithm);
    if (!result.success)
        return result;

    logInfo("All configuration files saved successfully");
    return ConfigResult(true);
}

ConfigResult ConfigManager::loadConfig(FileType type) {
    switch (type) {
    case FileType::System:
        return loadSystemConfig();
    case FileType::Facula:
        return loadFaculaConfig();
    case FileType::Hardware:
        return loadHardwareConfig();
    case FileType::Algorithm:
        return loadAlgorithmConfig();
    default:
        return ConfigResult(false, ErrorType::UnknownError, "Unknown config type");
    }
}

ConfigResult ConfigManager::saveConfig(FileType type) {
    switch (type) {
    case FileType::System:
        return saveSystemConfig();
    case FileType::Facula:
        return saveFaculaConfig();
    case FileType::Hardware:
        return saveHardwareConfig();
    case FileType::Algorithm:
        return saveAlgorithmConfig();
    default:
        return ConfigResult(false, ErrorType::UnknownError, "Unknown config type");
    }
}

QString ConfigManager::getConfigFilePath(FileType type) const {
    QString configDir = QApplication::applicationDirPath() + "/config/system/";

    switch (type) {
    case FileType::System:
        return configDir + "system_config.json";
    case FileType::Facula:
        return getModuleConfigFilePath("lenAdjust", type);
    case FileType::Hardware:
        return getModuleConfigFilePath("lenAdjust", type);
    case FileType::Algorithm:
        return getModuleConfigFilePath("lenAdjust", type);
    default:
        return QString();
    }
}

bool ConfigManager::configFileExists(FileType type) const {
    return QFile::exists(getConfigFilePath(type));
}

bool ConfigManager::ensureConfigDirectory() {
    QString configDir = QApplication::applicationDirPath() + "/config/";
    QDir    dir;

    if (!dir.exists(configDir)) {
        if (!dir.mkpath(configDir)) {
            logError("Failed to create config directory: " + configDir);
            return false;
        }
        logInfo("Created config directory: " + configDir);
    }

    return true;
}

void ConfigManager::setupFileWatcher() {
    // 添加配置文件到监控列表
    QStringList configFiles;
    configFiles << getConfigFilePath(FileType::System) << getConfigFilePath(FileType::Facula) << getConfigFilePath(FileType::Hardware)
                << getConfigFilePath(FileType::Algorithm);

    for (const QString &file : configFiles) {
        if (QFile::exists(file)) {
            m_fileWatcher->addPath(file);
        }
    }
}

void ConfigManager::onConfigFileChanged(const QString &path) {
    logInfo("Configuration file changed: " + path);

    // 确定配置文件类型并重新加载
    if (path == getConfigFilePath(FileType::System)) {
        loadConfig(FileType::System);
        Q_EMIT configChanged(FileType::System);
    } else if (path == getConfigFilePath(FileType::Facula)) {
        loadConfig(FileType::Facula);
        Q_EMIT configChanged(FileType::Facula);
    } else if (path == getConfigFilePath(FileType::Hardware)) {
        loadConfig(FileType::Hardware);
        Q_EMIT configChanged(FileType::Hardware);
    } else if (path == getConfigFilePath(FileType::Algorithm)) {
        loadConfig(FileType::Algorithm);
        Q_EMIT configChanged(FileType::Algorithm);
    }
}

void ConfigManager::logError(const QString &message) {
    qCritical() << "[ConfigManager]" << message;
    Q_EMIT configError(message);
}

void ConfigManager::logInfo(const QString &message) {
    qDebug() << "[ConfigManager]" << message;
}

void ConfigManager::logWarning(const QString &message) {
    qWarning() << "[ConfigManager]" << message;
}

// 系统配置加载
ConfigResult ConfigManager::loadSystemConfig() {
    QString filePath = getConfigFilePath(FileType::System);
    if (!QFile::exists(filePath)) {
        return ConfigResult(false, ErrorType::FileNotFound, "System config file not found: " + filePath);
    }

    QSettings settings(filePath, QSettings::IniFormat);

    // 读取系统配置
    m_systemConfig.version     = settings.value("SENSOR_BOARD/version", "2.6.1.6").toString();
    m_systemConfig.userid      = settings.value("MES/userid", "admin").toString();
    m_systemConfig.op          = settings.value("MES/op", "105").toString();
    m_systemConfig.work_number = settings.value("MES/work_number", "10001").toString();
    m_systemConfig.work_domain = settings.value("MES/work_domain", "001").toString();

    m_systemConfig.sensor_device       = settings.value("DEVICE/sensor_device", 4).toUInt();
    m_systemConfig.sensor_device_baud  = settings.value("DEVICE/sensor_device_baud", 230400).toUInt();
    m_systemConfig.station_number      = settings.value("DEVICE/station_number", 1).toUInt();
    m_systemConfig.clens_machine_brand = settings.value("DEVICE/clens_machine_brand", 3).toUInt();

    logInfo("System configuration loaded successfully");
    logInfo(
        QString("  Version: %1, Device: %2, Baud: %3").arg(m_systemConfig.version).arg(m_systemConfig.sensor_device).arg(m_systemConfig.sensor_device_baud));

    return ConfigResult(true);
}

// 光斑配置加载
ConfigResult ConfigManager::loadFaculaConfig() {
    QString filePath = getConfigFilePath(FileType::Facula);
    if (!QFile::exists(filePath)) {
        return ConfigResult(false, ErrorType::FileNotFound, "Facula config file not found: " + filePath);
    }

    QSettings settings(filePath, QSettings::IniFormat);

    // 读取多通道配置
    m_faculaConfig.facula_center_channels       = settings.value("FACULA_CENTER/facula_center_channels", "2,2").toString();
    m_faculaConfig.facula_center_peak_threshold = settings.value("FACULA_CENTER/facula_center_peak_threshold", 800).toUInt();

    // 解析多通道配置
    m_faculaConfig.facula_center_points = parseFaculaCenterChannels(m_faculaConfig.facula_center_channels);

    // 设置兼容性字段
    if (!m_faculaConfig.facula_center_points.isEmpty()) {
        m_faculaConfig.facula_center_loc_x = static_cast<uint8_t>(m_faculaConfig.facula_center_points.first().x());
        m_faculaConfig.facula_center_loc_y = static_cast<uint8_t>(m_faculaConfig.facula_center_points.first().y());
    }

    // 读取其他光斑参数
    m_faculaConfig.facula_ok_times    = settings.value("ADJUST_PARAM/facula_ok_times", 3).toUInt();
    m_faculaConfig.solid_time         = settings.value("ADJUST_PARAM/solid_time", 0).toUInt();
    m_faculaConfig.facula_ng_handle   = settings.value("ADJUST_PARAM/facula_ng_handle", 1).toUInt();
    m_faculaConfig.facula_handle_type = settings.value("FACULA_HANDLE/facula_handle_type", 1).toUInt();

    // 图像处理参数已移到AlgorithmConfig中，这里不再加载

    logInfo("Facula configuration loaded successfully");
    logInfo(QString("  Channels: %1, Peak threshold: %2, Handle type: %3")
                .arg(m_faculaConfig.facula_center_channels)
                .arg(m_faculaConfig.facula_center_peak_threshold)
                .arg(m_faculaConfig.facula_handle_type));

    return ConfigResult(true);
}

QString ConfigManager::getModuleConfigFilePath(const QString &moduleName, FileType type) const {
    QString configDir = QApplication::applicationDirPath() + "/config/modules/" + moduleName + "/";

    switch (type) {
    case FileType::System:
        return configDir + "system_config.json";
    case FileType::Facula:
        return configDir + "facula_config.json";
    case FileType::Hardware:
        return configDir + "hardware_config.json";
    case FileType::Algorithm:
        return configDir + "algorithm_config.json";
    default:
        return configDir + moduleName.toLower() + "_config.json";
    }
}

ConfigResult ConfigManager::migrateFromLegacyConfig() {
    ConfigResult result;
    result.success = true;
    result.message = "Legacy config migration completed";

    // 检查是否需要迁移
    QString legacyConfigPath = QApplication::applicationDirPath() + "/config/clen_config.xml";
    if (!QFile::exists(legacyConfigPath)) {
        result.message = "No legacy config found, migration skipped";
        return result;
    }

    qDebug() << "[INFO] Starting legacy config migration from:" << legacyConfigPath;

    // 执行各个配置的迁移
    ConfigResult faculaResult    = migrateFaculaConfig();
    ConfigResult algorithmResult = migrateAlgorithmConfig();
    ConfigResult hardwareResult  = migrateHardwareConfig();

    // 检查迁移结果
    if (!faculaResult.success || !algorithmResult.success || !hardwareResult.success) {
        result.success = false;
        result.message = QString("Migration failed - Facula: %1, Algorithm: %2, Hardware: %3")
                             .arg(faculaResult.success ? "OK" : "FAIL")
                             .arg(algorithmResult.success ? "OK" : "FAIL")
                             .arg(hardwareResult.success ? "OK" : "FAIL");
    }

    // 备份旧配置
    if (result.success) {
        backupLegacyConfigs();
    }

    return result;
}

ConfigResult ConfigManager::migrateFaculaConfig() {
    ConfigResult result;
    result.success = true;
    result.message = "Facula config migration completed";
    return result;
}

ConfigResult ConfigManager::migrateAlgorithmConfig() {
    ConfigResult result;
    result.success = true;
    result.message = "Algorithm config migration completed";
    return result;
}

ConfigResult ConfigManager::migrateHardwareConfig() {
    ConfigResult result;
    result.success = true;
    result.message = "Hardware config migration completed";
    return result;
}

ConfigResult ConfigManager::backupLegacyConfigs() {
    ConfigResult result;
    result.success = true;
    result.message = "Legacy config backup completed";

    // 创建备份目录
    QString backupDir = QApplication::applicationDirPath() + "/config/backup/";
    QDir().mkpath(backupDir);

    // 备份旧的XML配置文件
    QString legacyConfigPath = QApplication::applicationDirPath() + "/config/clen_config.xml";
    if (QFile::exists(legacyConfigPath)) {
        QString timestamp  = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
        QString backupPath = backupDir + "clen_config_" + timestamp + ".xml";

        if (QFile::copy(legacyConfigPath, backupPath)) {
            qDebug() << "[INFO] Legacy config backed up to:" << backupPath;
        } else {
            result.success = false;
            result.message = "Failed to backup legacy config";
        }
    }

    return result;
}

// 按职责分离的配置获取接口实现
DeviceConfig ConfigManager::getDeviceConfig() const {
    DeviceConfig deviceConfig;

    // 从SystemConfig中提取设备相关配置
    deviceConfig.sensor_device       = m_systemConfig.sensor_device;
    deviceConfig.sensor_device_baud  = m_systemConfig.sensor_device_baud;
    deviceConfig.clens_machine_brand = m_systemConfig.clens_machine_brand;

    return deviceConfig;
}

ProcessConfig ConfigManager::getProcessConfig() const {
    ProcessConfig processConfig;

    // 从FaculaConfig中提取流程相关配置
    processConfig.facula_ok_times  = m_faculaConfig.facula_ok_times;
    processConfig.solid_time       = m_faculaConfig.solid_time;
    processConfig.facula_ng_handle = m_faculaConfig.facula_ng_handle;

    return processConfig;
}

}  // namespace Config
