#ifndef CONFIG_MANAGER_H
#define CONFIG_MANAGER_H

#include "ConfigTypes.h"
#include <QFileSystemWatcher>
#include <QMutex>
#include <QObject>
#include <QSettings>

namespace Config {

/**
 * @brief 统一的配置管理器类
 *
 * 负责管理所有配置文件的加载、保存、验证和自动生成
 * 支持配置文件热重载和模块化设计
 */
class ConfigManager : public QObject {
    Q_OBJECT

  public:
    static ConfigManager *getInstance();

    // 配置加载和保存
    ConfigResult loadAllConfigs();
    ConfigResult saveAllConfigs();
    ConfigResult loadConfig(FileType type);
    ConfigResult saveConfig(FileType type);

    // 配置数据访问
    const SystemConfig &getSystemConfig() const {
        return m_systemConfig;
    }
    const FaculaConfig &getFaculaConfig() const {
        return m_faculaConfig;
    }
    const HardwareConfig &getHardwareConfig() const {
        return m_hardwareConfig;
    }
    const AlgorithmConfig &getAlgorithmConfig() const {
        return m_algorithmConfig;
    }

    // 配置数据修改
    void setSystemConfig(const SystemConfig &config);
    void setFaculaConfig(const FaculaConfig &config);
    void setHardwareConfig(const HardwareConfig &config);
    void setAlgorithmConfig(const AlgorithmConfig &config);

    // 配置文件管理
    ConfigResult generateDefaultConfigs();
    ConfigResult validateConfigs();

    // 配置文件路径
    QString getConfigFilePath(FileType type) const;
    QString getModuleConfigFilePath(const QString &moduleName, FileType type) const;
    bool    configFileExists(FileType type) const;
    bool    moduleConfigFileExists(const QString &moduleName, FileType type) const;

    // 模块化配置管理
    ConfigResult loadModuleConfig(const QString &moduleName, FileType type);
    ConfigResult saveModuleConfig(const QString &moduleName, FileType type);
    ConfigResult generateDefaultModuleConfig(const QString &moduleName, FileType type);

    // 配置格式支持
    ConfigFormat detectConfigFormat(const QString &filePath) const;
    ConfigResult convertConfigFormat(const QString &inputFile, const QString &outputFile, ConfigFormat fromFormat, ConfigFormat toFormat);

    // 多通道配置解析
    QVector<QPoint> parseFaculaCenterChannels(const QString &channelsStr);
    bool            isValidChannelPoint(const QPoint &point, uint8_t maxX = 255, uint8_t maxY = 255);

  Q_SIGNALS:
    void configChanged(FileType type);
    void configError(const QString &error);

  private Q_SLOTS:
    void onConfigFileChanged(const QString &path);

  private:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();

    static ConfigManager *s_instance;
    static QMutex         s_mutex;

    // 配置数据
    SystemConfig    m_systemConfig;
    FaculaConfig    m_faculaConfig;
    HardwareConfig  m_hardwareConfig;
    AlgorithmConfig m_algorithmConfig;

    // 文件监控
    QFileSystemWatcher *m_fileWatcher;

    // 内部方法
    ConfigResult loadSystemConfig();
    ConfigResult loadFaculaConfig();
    ConfigResult loadHardwareConfig();
    ConfigResult loadAlgorithmConfig();

    ConfigResult saveSystemConfig();
    ConfigResult saveFaculaConfig();
    ConfigResult saveHardwareConfig();
    ConfigResult saveAlgorithmConfig();

    ConfigResult generateDefaultSystemConfig();
    ConfigResult generateDefaultFaculaConfig();
    ConfigResult generateDefaultHardwareConfig();
    ConfigResult generateDefaultAlgorithmConfig();

    // JSON格式支持
    ConfigResult loadConfigFromJson(const QString &filePath, FileType type);
    ConfigResult saveConfigToJson(const QString &filePath, FileType type);

    // JSON配置加载方法
    ConfigResult loadSystemConfigFromJson(const QJsonObject &obj);
    ConfigResult loadFaculaConfigFromJson(const QJsonObject &obj);
    ConfigResult loadHardwareConfigFromJson(const QJsonObject &obj);
    ConfigResult loadAlgorithmConfigFromJson(const QJsonObject &obj);

    // JSON配置保存方法
    void saveSystemConfigToJson(QJsonObject &obj);
    void saveFaculaConfigToJson(QJsonObject &obj);
    void saveHardwareConfigToJson(QJsonObject &obj);
    void saveAlgorithmConfigToJson(QJsonObject &obj);

    // 配置迁移
    ConfigResult migrateFromLegacyConfig();
    ConfigResult migrateFaculaConfig();
    ConfigResult migrateAlgorithmConfig();
    ConfigResult migrateHardwareConfig();
    ConfigResult backupLegacyConfigs();

    void setupFileWatcher();
    void logError(const QString &message);
    void logInfo(const QString &message);
    void logWarning(const QString &message);

    // 确保配置目录存在
    bool ensureConfigDirectory();
    bool ensureModuleConfigDirectory(const QString &moduleName);
};

}  // namespace Config

#endif  // CONFIG_MANAGER_H
