#pragma once

#include "ConfigTypes.h"
#include "IModuleConfigManager.h"
#include <QFileSystemWatcher>
#include <QObject>
#include <QVariantMap>

namespace Config {

/**
 * @brief 算法配置管理器
 *
 * 专门负责算法参数相关的配置管理
 * 职责：
 * - 管理算法计算参数
 * - 管理图像处理参数
 * - 提供算法配置的访问接口
 */
class AlgorithmConfigManager : public QObject, public IModuleConfigManager {
    Q_OBJECT

  public:
    explicit AlgorithmConfigManager(QObject *parent = nullptr);
    ~AlgorithmConfigManager() override;

    // IModuleConfigManager 接口实现
    ConfigResult loadConfig() override;
    ConfigResult saveConfig() override;
    bool         isConfigValid() const override;
    QString      getConfigFilePath() const override;
    QString      getModuleName() const override {
        return "Algorithm";
    }

    /**
     * @brief 获取算法配置
     * @return 算法配置引用
     */
    const AlgorithmConfig &getConfig() const {
        return m_config;
    }

    /**
     * @brief 设置算法配置
     * @param config 新的算法配置
     */
    void setConfig(const AlgorithmConfig &config);

    /**
     * @brief 获取算法参数
     * @param key 参数名
     * @param defaultValue 默认值
     * @return 参数值
     */
    QVariant getParameter(const QString &key, const QVariant &defaultValue = QVariant()) const;

    /**
     * @brief 设置算法参数
     * @param key 参数名
     * @param value 参数值
     */
    void setParameter(const QString &key, const QVariant &value);

    /**
     * @brief 获取所有算法参数
     * @return 参数映射表
     */
    const QVariantMap &getAllParameters() const {
        return m_config.parameters;
    }

    /**
     * @brief 获取图像处理配置
     * @return 图像处理配置引用
     */
    const ProcessingConfig &getProcessingConfig() const {
        return m_config.processing;
    }

    /**
     * @brief 设置图像处理配置
     * @param config 图像处理配置
     */
    void setProcessingConfig(const ProcessingConfig &config);

  Q_SIGNALS:
    /**
     * @brief 配置变更信号
     */
    void configChanged();

    /**
     * @brief 参数变更信号
     * @param key 参数名
     * @param value 新值
     */
    void parameterChanged(const QString &key, const QVariant &value);

  private Q_SLOTS:
    /**
     * @brief 配置文件变更处理
     * @param path 文件路径
     */
    void onFileChanged(const QString &path);

  private:
    /**
     * @brief 验证配置参数
     * @return 验证结果
     */
    ConfigResult validateConfig() const;

    /**
     * @brief 生成默认配置
     */
    void generateDefaultConfig();

    /**
     * @brief 加载默认算法参数
     */
    void loadDefaultParameters();

  private:
    AlgorithmConfig     m_config;          // 算法配置数据
    QFileSystemWatcher *m_fileWatcher;     // 文件监控器
    QString             m_configFilePath;  // 配置文件路径
};

}  // namespace Config
