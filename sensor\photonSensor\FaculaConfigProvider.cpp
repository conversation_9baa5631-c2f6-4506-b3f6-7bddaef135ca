#include "FaculaConfigProvider.h"
#include <QApplication>
#include <QRegularExpression>
#include <QDebug>

namespace Facula {

// 静态常量定义
const QString FaculaConfigProvider::KEY_FACULA_CENTER_CHANNELS = "facula_center_channels";
const QString FaculaConfigProvider::KEY_FACULA_CENTER_PEAK_THRESHOLD = "facula_center_peak_threshold";
const QString FaculaConfigProvider::KEY_FACULA_CENTER_LOC_X = "facula_center_loc_x";
const QString FaculaConfigProvider::KEY_FACULA_CENTER_LOC_Y = "facula_center_loc_y";
const QString FaculaConfigProvider::KEY_FACULA_HANDLE_TYPE = "facula_handle_type";

const QString FaculaConfigProvider::DEFAULT_CHANNELS = "2,2";
const uint32_t FaculaConfigProvider::DEFAULT_PEAK_THRESHOLD = 800;
const uint8_t FaculaConfigProvider::DEFAULT_LOC_X = 2;
const uint8_t FaculaConfigProvider::DEFAULT_LOC_Y = 2;
const uint8_t FaculaConfigProvider::DEFAULT_HANDLE_TYPE = 1;

const uint32_t FaculaConfigProvider::MIN_PEAK_THRESHOLD = 100;
const uint32_t FaculaConfigProvider::MAX_PEAK_THRESHOLD = 2000;
const uint8_t FaculaConfigProvider::MAX_HANDLE_TYPE = 2;

FaculaConfigProvider::FaculaConfigProvider(QObject *parent)
    : BaseConfigProvider(parent)
{
    logInfo("FaculaConfigProvider initialized");
}

QString FaculaConfigProvider::getConfigFilePath() const {
    return QApplication::applicationDirPath() + "/config/modules/facula/facula_config.ini";
}

void FaculaConfigProvider::loadDefaultParameters() {
    // 设置默认参数值
    setParameterDefault(KEY_FACULA_CENTER_CHANNELS, DEFAULT_CHANNELS);
    setParameterDefault(KEY_FACULA_CENTER_PEAK_THRESHOLD, DEFAULT_PEAK_THRESHOLD);
    setParameterDefault(KEY_FACULA_CENTER_LOC_X, DEFAULT_LOC_X);
    setParameterDefault(KEY_FACULA_CENTER_LOC_Y, DEFAULT_LOC_Y);
    setParameterDefault(KEY_FACULA_HANDLE_TYPE, DEFAULT_HANDLE_TYPE);

    // 设置参数信息
    setParameterInfo(KEY_FACULA_CENTER_CHANNELS, 
                    "光斑中心通道配置，格式：x1,y1;x2,y2", 
                    "QString", 
                    "坐标格式，如：2,2 或 2,2;3,3");
    
    setParameterInfo(KEY_FACULA_CENTER_PEAK_THRESHOLD, 
                    "光斑中心峰值阈值", 
                    "uint32_t", 
                    QString("%1-%2").arg(MIN_PEAK_THRESHOLD).arg(MAX_PEAK_THRESHOLD));
    
    setParameterInfo(KEY_FACULA_CENTER_LOC_X, 
                    "光斑中心X坐标（兼容性参数）", 
                    "uint8_t", 
                    "0-255");
    
    setParameterInfo(KEY_FACULA_CENTER_LOC_Y, 
                    "光斑中心Y坐标（兼容性参数）", 
                    "uint8_t", 
                    "0-255");
    
    setParameterInfo(KEY_FACULA_HANDLE_TYPE, 
                    "光斑处理类型：0-基础，1-增强，2-高精度", 
                    "uint8_t", 
                    QString("0-%1").arg(MAX_HANDLE_TYPE));

    logInfo("Default facula parameters loaded");
}

Config::ConfigResult FaculaConfigProvider::validateParameter(const QString &key, const QVariant &value) const {
    // 先调用基类验证
    Config::ConfigResult baseResult = BaseConfigProvider::validateParameter(key, value);
    if (!baseResult.success) {
        return baseResult;
    }

    // 光斑特定的验证
    if (key == KEY_FACULA_CENTER_CHANNELS) {
        QString channels = value.toString();
        if (!isValidChannelFormat(channels)) {
            return Config::ConfigResult(false, Config::ErrorType::ValidationError, 
                                      QString("Invalid channel format: %1. Expected format: x,y or x1,y1;x2,y2").arg(channels));
        }
    }
    else if (key == KEY_FACULA_CENTER_PEAK_THRESHOLD) {
        uint32_t threshold = value.toUInt();
        if (!isValidThreshold(threshold)) {
            return Config::ConfigResult(false, Config::ErrorType::ValidationError, 
                                      QString("Peak threshold %1 out of range [%2-%3]").arg(threshold).arg(MIN_PEAK_THRESHOLD).arg(MAX_PEAK_THRESHOLD));
        }
    }
    else if (key == KEY_FACULA_HANDLE_TYPE) {
        uint8_t handleType = value.toUInt();
        if (!isValidHandleType(handleType)) {
            return Config::ConfigResult(false, Config::ErrorType::ValidationError, 
                                      QString("Handle type %1 out of range [0-%2]").arg(handleType).arg(MAX_HANDLE_TYPE));
        }
    }
    else if (key == KEY_FACULA_CENTER_LOC_X || key == KEY_FACULA_CENTER_LOC_Y) {
        uint8_t coord = value.toUInt();
        if (coord > 255) {
            return Config::ConfigResult(false, Config::ErrorType::ValidationError, 
                                      QString("Coordinate %1 out of range [0-255]").arg(coord));
        }
    }

    return Config::ConfigResult(true);
}

QString FaculaConfigProvider::getParameterRange(const QString &key) const {
    if (key == KEY_FACULA_CENTER_CHANNELS) {
        return "坐标格式，如：2,2 或 2,2;3,3";
    }
    else if (key == KEY_FACULA_CENTER_PEAK_THRESHOLD) {
        return QString("%1-%2").arg(MIN_PEAK_THRESHOLD).arg(MAX_PEAK_THRESHOLD);
    }
    else if (key == KEY_FACULA_HANDLE_TYPE) {
        return QString("0-%1").arg(MAX_HANDLE_TYPE);
    }
    else if (key == KEY_FACULA_CENTER_LOC_X || key == KEY_FACULA_CENTER_LOC_Y) {
        return "0-255";
    }
    
    return BaseConfigProvider::getParameterRange(key);
}

void FaculaConfigProvider::onParameterChanged(const QString &key, const QVariant &oldValue, const QVariant &newValue) {
    BaseConfigProvider::onParameterChanged(key, oldValue, newValue);
    
    // 当通道配置变更时，更新兼容性坐标
    if (key == KEY_FACULA_CENTER_CHANNELS) {
        updateCompatibilityCoordinates();
        logInfo(QString("Facula center channels updated: %1 -> %2").arg(oldValue.toString(), newValue.toString()));
    }
    else if (key == KEY_FACULA_CENTER_PEAK_THRESHOLD) {
        logInfo(QString("Peak threshold updated: %1 -> %2").arg(oldValue.toUInt()).arg(newValue.toUInt()));
    }
    else if (key == KEY_FACULA_HANDLE_TYPE) {
        logInfo(QString("Handle type updated: %1 -> %2").arg(oldValue.toUInt()).arg(newValue.toUInt()));
    }
}

QString FaculaConfigProvider::getFaculaCenterChannels() const {
    return getParameter(KEY_FACULA_CENTER_CHANNELS, DEFAULT_CHANNELS).toString();
}

void FaculaConfigProvider::setFaculaCenterChannels(const QString &channels) {
    setParameter(KEY_FACULA_CENTER_CHANNELS, channels);
}

QVector<QPoint> FaculaConfigProvider::getFaculaCenterPoints() const {
    QString channels = getFaculaCenterChannels();
    return parseChannels(channels);
}

uint32_t FaculaConfigProvider::getPeakThreshold() const {
    return getParameter(KEY_FACULA_CENTER_PEAK_THRESHOLD, DEFAULT_PEAK_THRESHOLD).toUInt();
}

void FaculaConfigProvider::setPeakThreshold(uint32_t threshold) {
    setParameter(KEY_FACULA_CENTER_PEAK_THRESHOLD, threshold);
}

uint8_t FaculaConfigProvider::getHandleType() const {
    return getParameter(KEY_FACULA_HANDLE_TYPE, DEFAULT_HANDLE_TYPE).toUInt();
}

void FaculaConfigProvider::setHandleType(uint8_t handleType) {
    setParameter(KEY_FACULA_HANDLE_TYPE, handleType);
}

uint8_t FaculaConfigProvider::getFaculaCenterLocX() const {
    return getParameter(KEY_FACULA_CENTER_LOC_X, DEFAULT_LOC_X).toUInt();
}

uint8_t FaculaConfigProvider::getFaculaCenterLocY() const {
    return getParameter(KEY_FACULA_CENTER_LOC_Y, DEFAULT_LOC_Y).toUInt();
}

QVector<QPoint> FaculaConfigProvider::parseChannels(const QString &channels) const {
    QVector<QPoint> points;
    
    if (channels.isEmpty()) {
        return points;
    }
    
    // 支持两种格式：
    // 1. 单点格式：x,y
    // 2. 多点格式：x1,y1;x2,y2;...
    QStringList channelList = channels.split(';', Qt::SkipEmptyParts);
    
    for (const QString &channel : channelList) {
        QStringList coords = channel.split(',', Qt::SkipEmptyParts);
        if (coords.size() == 2) {
            bool okX, okY;
            int x = coords[0].trimmed().toInt(&okX);
            int y = coords[1].trimmed().toInt(&okY);
            if (okX && okY && x >= 0 && y >= 0) {
                points.append(QPoint(x, y));
            }
        }
    }
    
    return points;
}

void FaculaConfigProvider::updateCompatibilityCoordinates() {
    QVector<QPoint> points = getFaculaCenterPoints();
    if (!points.isEmpty()) {
        // 使用第一个点作为兼容性坐标
        QPoint firstPoint = points.first();
        setParameter(KEY_FACULA_CENTER_LOC_X, static_cast<uint8_t>(firstPoint.x()));
        setParameter(KEY_FACULA_CENTER_LOC_Y, static_cast<uint8_t>(firstPoint.y()));
    }
}

bool FaculaConfigProvider::isValidChannelFormat(const QString &channels) const {
    if (channels.isEmpty()) {
        return false;
    }
    
    // 使用正则表达式验证格式
    // 支持格式：数字,数字 或 数字,数字;数字,数字;...
    QRegularExpression regex(R"(^\d+,\d+(?:;\d+,\d+)*$)");
    return regex.match(channels).hasMatch();
}

bool FaculaConfigProvider::isValidThreshold(uint32_t threshold) const {
    return threshold >= MIN_PEAK_THRESHOLD && threshold <= MAX_PEAK_THRESHOLD;
}

bool FaculaConfigProvider::isValidHandleType(uint8_t handleType) const {
    return handleType <= MAX_HANDLE_TYPE;
}

} // namespace Facula
